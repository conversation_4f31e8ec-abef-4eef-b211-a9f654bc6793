package dataconv

import (
	"testing"

	"github.com/vmihailenco/msgpack/v5"
)

// 测试结构体
type TestStruct struct {
	Name     string                 `msgpack:"name"`
	Age      string                 `msgpack:"age" convert:"numberToString"`
	Score    int64                  `msgpack:"score" convert:"stringToNumber"`
	Secret   string                 `msgpack:"secret" rawlog:"exclude"`
	RawLog   map[string]interface{} `msgpack:"-"`
}

func TestMsgpackDecodeWithConversion(t *testing.T) {
	// 创建测试数据
	data := map[string]interface{}{
		"name":     "<PERSON>",
		"age":      25,           // 数字，应该转换为字符串
		"score":    "95",         // 字符串，应该转换为数字
		"secret":   "confidential", // 应该设置到字段但不写入RawLog
		"unknown":  "extra_data", // 未知字段，应该写入RawLog
	}

	// 编码为msgpack
	msgpackData, err := msgpack.Marshal(data)
	if err != nil {
		t.Fatalf("Failed to marshal data: %v", err)
	}

	// 解码
	var result TestStruct
	err = MsgpackDecode(msgpackData, &result)
	if err != nil {
		t.Fatalf("Failed to decode: %v", err)
	}

	// 验证结果
	if result.Name != "John" {
		t.Errorf("Expected Name to be 'John', got '%s'", result.Name)
	}

	if result.Age != "25" {
		t.Errorf("Expected Age to be '25' (string), got '%s'", result.Age)
	}

	if result.Score != 95 {
		t.Errorf("Expected Score to be 95 (int64), got %d", result.Score)
	}

	if result.Secret != "confidential" {
		t.Errorf("Expected Secret to be 'confidential', got '%s'", result.Secret)
	}

	// 验证RawLog
	if result.RawLog == nil {
		t.Fatal("RawLog should not be nil")
	}

	// name, age, score 应该在RawLog中（默认行为）
	if result.RawLog["name"] != "John" {
		t.Errorf("Expected RawLog['name'] to be 'John', got %v", result.RawLog["name"])
	}

	if result.RawLog["age"] != "25" {
		t.Errorf("Expected RawLog['age'] to be '25' (converted), got %v", result.RawLog["age"])
	}

	if result.RawLog["score"] != int64(95) {
		t.Errorf("Expected RawLog['score'] to be 95 (converted), got %v", result.RawLog["score"])
	}

	// secret 不应该在RawLog中（exclude标记）
	if _, exists := result.RawLog["secret"]; exists {
		t.Error("secret should not be in RawLog due to exclude tag")
	}

	// unknown 应该在RawLog中
	if result.RawLog["unknown"] != "extra_data" {
		t.Errorf("Expected RawLog['unknown'] to be 'extra_data', got %v", result.RawLog["unknown"])
	}
}

func TestMsgpackDecodeWithoutConversion(t *testing.T) {
	// 测试不带转换的普通结构体
	type SimpleStruct struct {
		Name   string                 `msgpack:"name"`
		Age    int                    `msgpack:"age"`
		RawLog map[string]interface{} `msgpack:"-"`
	}

	data := map[string]interface{}{
		"name":    "Alice",
		"age":     30,
		"unknown": "extra",
	}

	msgpackData, err := msgpack.Marshal(data)
	if err != nil {
		t.Fatalf("Failed to marshal data: %v", err)
	}

	var result SimpleStruct
	err = MsgpackDecode(msgpackData, &result)
	if err != nil {
		t.Fatalf("Failed to decode: %v", err)
	}

	// 验证基本字段
	if result.Name != "Alice" {
		t.Errorf("Expected Name to be 'Alice', got '%s'", result.Name)
	}

	if result.Age != 30 {
		t.Errorf("Expected Age to be 30, got %d", result.Age)
	}

	// 验证RawLog包含所有字段（默认行为）
	if result.RawLog["name"] != "Alice" {
		t.Errorf("Expected RawLog['name'] to be 'Alice', got %v", result.RawLog["name"])
	}

	// age 字段可能是不同的整数类型，需要灵活处理
	ageVal := result.RawLog["age"]
	var ageInt int64
	switch v := ageVal.(type) {
	case int8:
		ageInt = int64(v)
	case int16:
		ageInt = int64(v)
	case int32:
		ageInt = int64(v)
	case int64:
		ageInt = v
	case int:
		ageInt = int64(v)
	default:
		t.Errorf("Expected RawLog['age'] to be an integer type, got %v (type: %T)", ageVal, ageVal)
	}
	if ageInt != 30 {
		t.Errorf("Expected RawLog['age'] to be 30, got %d", ageInt)
	}

	if result.RawLog["unknown"] != "extra" {
		t.Errorf("Expected RawLog['unknown'] to be 'extra', got %v", result.RawLog["unknown"])
	}
}
