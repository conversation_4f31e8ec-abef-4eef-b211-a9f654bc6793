package dataconv

import (
	"fmt"
	"strconv"
	"time"

	"github.com/vmihailenco/msgpack/v5"
	"github.com/vmihailenco/msgpack/v5/msgpcode"
)

// StringInt 是一个可以从字符串解析为整数的类型
type StringInt int

// StringBool 是一个可以从字符串解析为布尔值的类型
type StringBool bool

// StringFloat 是一个可以从字符串解析为浮点数的类型
type StringFloat float64

// FlexibleTime 是一个可以从多种格式解析时间的类型
type FlexibleTime time.Time

// NumberString 是一个可以将数值编码为字符串的类型
type NumberString string

// DecodeMsgpack 实现 msgpack.CustomDecoder 接口
func (si *StringInt) DecodeMsgpack(dec *msgpack.Decoder) error {
	c, err := dec.PeekCode()
	if err != nil {
		return err
	}

	if msgpcode.IsFixedNum(c) || c == msgpcode.Int8 || c == msgpcode.Int16 ||
		c == msgpcode.Int32 || c == msgpcode.Int64 || c == msgpcode.Uint8 ||
		c == msgpcode.Uint16 || c == msgpcode.Uint32 || c == msgpcode.Uint64 {
		var i int
		if err := dec.Decode(&i); err != nil {
			return err
		}
		*si = StringInt(i)
		return nil
	}

	if msgpcode.IsString(c) {
		var s string
		if err := dec.Decode(&s); err != nil {
			return err
		}
		i, err := strconv.Atoi(s)
		if err != nil {
			return err
		}
		*si = StringInt(i)
		return nil
	}

	return fmt.Errorf("cannot decode %x into StringInt", c)
}

// EncodeMsgpack 实现 msgpack.CustomEncoder 接口
func (si StringInt) EncodeMsgpack(enc *msgpack.Encoder) error {
	return enc.Encode(int(si))
}

// DecodeMsgpack 实现 msgpack.CustomDecoder 接口
func (sb *StringBool) DecodeMsgpack(dec *msgpack.Decoder) error {
	c, err := dec.PeekCode()
	if err != nil {
		return err
	}

	if c == msgpcode.True || c == msgpcode.False {
		var b bool
		if err := dec.Decode(&b); err != nil {
			return err
		}
		*sb = StringBool(b)
		return nil
	}

	if msgpcode.IsString(c) {
		var s string
		if err := dec.Decode(&s); err != nil {
			return err
		}
		b, err := strconv.ParseBool(s)
		if err != nil {
			return err
		}
		*sb = StringBool(b)
		return nil
	}

	return fmt.Errorf("cannot decode %x into StringBool", c)
}

// EncodeMsgpack 实现 msgpack.CustomEncoder 接口
func (sb StringBool) EncodeMsgpack(enc *msgpack.Encoder) error {
	return enc.Encode(bool(sb))
}

// DecodeMsgpack 实现 msgpack.CustomDecoder 接口
func (sf *StringFloat) DecodeMsgpack(dec *msgpack.Decoder) error {
	c, err := dec.PeekCode()
	if err != nil {
		return err
	}

	if c == msgpcode.Float {
		var f float64
		if err := dec.Decode(&f); err != nil {
			return err
		}
		*sf = StringFloat(f)
		return nil
	}

	if msgpcode.IsFixedNum(c) || c == msgpcode.Int8 || c == msgpcode.Int16 ||
		c == msgpcode.Int32 || c == msgpcode.Int64 || c == msgpcode.Uint8 ||
		c == msgpcode.Uint16 || c == msgpcode.Uint32 || c == msgpcode.Uint64 {
		var i int64
		if err := dec.Decode(&i); err != nil {
			return err
		}
		*sf = StringFloat(float64(i))
		return nil
	}

	if msgpcode.IsString(c) {
		var s string
		if err := dec.Decode(&s); err != nil {
			return err
		}
		f, err := strconv.ParseFloat(s, 64)
		if err != nil {
			return err
		}
		*sf = StringFloat(f)
		return nil
	}

	return fmt.Errorf("cannot decode %x into StringFloat", c)
}

// EncodeMsgpack 实现 msgpack.CustomEncoder 接口
func (sf StringFloat) EncodeMsgpack(enc *msgpack.Encoder) error {
	return enc.Encode(float64(sf))
}

// DecodeMsgpack 实现 msgpack.CustomDecoder 接口
func (ft *FlexibleTime) DecodeMsgpack(dec *msgpack.Decoder) error {
	c, err := dec.PeekCode()
	if err != nil {
		return err
	}

	if c == msgpcode.Ext8 || c == msgpcode.Ext16 || c == msgpcode.Ext32 ||
		(c >= msgpcode.FixExt1 && c <= msgpcode.FixExt16) {
		var t time.Time
		if err := dec.Decode(&t); err != nil {
			return err
		}
		*ft = FlexibleTime(t)
		return nil
	}

	if msgpcode.IsString(c) {
		var timeStr string
		if err := dec.Decode(&timeStr); err != nil {
			return err
		}

		formats := []string{
			time.RFC3339,
			"2006-01-02T15:04:05",
			"2006-01-02 15:04:05",
			"2006-01-02",
		}

		for _, format := range formats {
			if t, err := time.Parse(format, timeStr); err == nil {
				*ft = FlexibleTime(t)
				return nil
			}
		}

		return fmt.Errorf("unable to parse time from: %s", timeStr)
	}

	return fmt.Errorf("cannot decode %x into FlexibleTime", c)
}

// EncodeMsgpack 实现 msgpack.CustomEncoder 接口
func (ft FlexibleTime) EncodeMsgpack(enc *msgpack.Encoder) error {
	return enc.Encode(time.Time(ft))
}

// Time 返回 time.Time 类型的值
func (ft FlexibleTime) Time() time.Time {
	return time.Time(ft)
}

// String 返回格式化的时间字符串
func (ft FlexibleTime) String() string {
	return time.Time(ft).Format(time.RFC3339)
}

// DecodeMsgpack 实现 msgpack.CustomDecoder 接口
func (ns *NumberString) DecodeMsgpack(dec *msgpack.Decoder) error {
	c, err := dec.PeekCode()
	if err != nil {
		return err
	}

	// 如果是整数类型
	if msgpcode.IsFixedNum(c) || c == msgpcode.Int8 || c == msgpcode.Int16 ||
		c == msgpcode.Int32 || c == msgpcode.Int64 || c == msgpcode.Uint8 ||
		c == msgpcode.Uint16 || c == msgpcode.Uint32 || c == msgpcode.Uint64 {
		var i int64
		if err := dec.Decode(&i); err != nil {
			return err
		}
		*ns = NumberString(strconv.FormatInt(i, 10))
		return nil
	}

	// 如果是浮点数类型
	if c == msgpcode.Float || c == msgpcode.Double {
		var f float64
		if err := dec.Decode(&f); err != nil {
			return err
		}
		*ns = NumberString(strconv.FormatFloat(f, 'f', -1, 64))
		return nil
	}

	// 如果是字符串类型，直接解码
	if msgpcode.IsString(c) {
		var s string
		if err := dec.Decode(&s); err != nil {
			return err
		}
		*ns = NumberString(s)
		return nil
	}

	return fmt.Errorf("cannot decode %x into NumberString", c)
}

// EncodeMsgpack 实现 msgpack.CustomEncoder 接口
func (ns NumberString) EncodeMsgpack(enc *msgpack.Encoder) error {
	// 直接编码为字符串
	return enc.Encode(string(ns))
}
