package dataconv

import (
	"reflect"
	"strings"
	"time"

	"github.com/mitchellh/mapstructure"
)

// typeInfo 存储结构体类型的元信息，用于转换过程
type typeInfo struct {
	convertFields map[string]convertInfo     // 需要类型转换的字段信息
	rawDataField  string                     // 存储原始数据的字段名
	nestedFields  map[string]nestedFieldInfo // 嵌套结构体字段信息
	allFields     map[string]string          // 所有字段的映射 (mapstructure标签值 -> 字段名)
}

// nestedFieldInfo 存储嵌套字段的元信息
type nestedFieldInfo struct {
	fieldType reflect.Type // 字段类型
	isSlice   bool         // 是否是切片类型
	fieldName string       // 结构体中的字段名
}

// convertInfo 存储类型转换信息
type convertInfo struct {
	fieldName string // 结构体中的字段名
	convType  string // 转换类型，如"stringToInt"
}

// MapToStruct 将map转换为结构体
func MapToStruct(data map[string]interface{}, target interface{}) error {
	targetType := reflect.TypeOf(target).Elem()
	typeInfo := getTypeInfo(targetType)

	processedData, err := preprocessData(data, typeInfo)
	if err != nil {
		return err
	}

	decoderConfig := &mapstructure.DecoderConfig{
		Result:  target,
		TagName: "mapstructure",
		DecodeHook: mapstructure.ComposeDecodeHookFunc(
			mapstructure.StringToTimeHookFunc("2006-01-02"),
		),
	}

	decoder, err := mapstructure.NewDecoder(decoderConfig)
	if err != nil {
		return err
	}

	if err := decoder.Decode(processedData); err != nil {
		return err
	}

	processRawData(data, target, typeInfo)
	return nil
}

// getTypeInfo 获取结构体类型的元信息
func getTypeInfo(typ reflect.Type) typeInfo {
	info := typeInfo{
		convertFields: make(map[string]convertInfo),
		nestedFields:  make(map[string]nestedFieldInfo),
		allFields:     make(map[string]string),
	}

	if typ.Kind() != reflect.Struct {
		return info
	}

	for i := 0; i < typ.NumField(); i++ {
		field := typ.Field(i)
		mapTag := field.Tag.Get("mapstructure")
		if mapTag == "-" {
			continue
		}

		fieldName := mapTag
		if fieldName == "" {
			fieldName = field.Name
		} else {
			fieldName = strings.Split(fieldName, ",")[0]
		}

		// 记录所有字段
		info.allFields[fieldName] = field.Name

		rawdataTag := field.Tag.Get("rawdata")
		if rawdataTag == "target" {
			info.rawDataField = field.Name
		}

		convertTag := field.Tag.Get("convert")
		if convertTag != "" {
			info.convertFields[fieldName] = convertInfo{
				fieldName: field.Name,
				convType:  convertTag,
			}
		}

		fieldType := field.Type
		isSlice := false

		if fieldType.Kind() == reflect.Ptr {
			fieldType = fieldType.Elem()
		}

		if fieldType.Kind() == reflect.Slice {
			fieldType = fieldType.Elem()
			isSlice = true

			if fieldType.Kind() == reflect.Ptr {
				fieldType = fieldType.Elem()
			}
		}

		if fieldType.Kind() == reflect.Struct && fieldType != reflect.TypeOf(time.Time{}) {
			info.nestedFields[fieldName] = nestedFieldInfo{
				fieldType: fieldType,
				isSlice:   isSlice,
				fieldName: field.Name,
			}
		}
	}

	return info
}

// preprocessData 预处理map数据
func preprocessData(data map[string]interface{}, info typeInfo) (map[string]interface{}, error) {
	// 1. 创建结果map，初始复制原始数据
	result := make(map[string]interface{}, len(data))
	for k, v := range data {
		result[k] = v
	}

	// 2. 处理需要类型转换的字段
	for fieldName, convInfo := range info.convertFields {
		if val, exists := result[fieldName]; exists {
			// 执行实际类型转换
			result[fieldName] = convertValue(val, convInfo.convType)
		}
	}

	// 3. 处理嵌套结构体字段
	for fieldName, nestedInfo := range info.nestedFields {
		if val, exists := result[fieldName]; exists {
			// 获取嵌套结构体的类型信息
			nestedTypeInfo := getTypeInfo(nestedInfo.fieldType)

			if nestedInfo.isSlice {
				// 3.1 处理切片类型的嵌套结构体
				if sliceVal, ok := val.([]map[string]interface{}); ok {
					processedSlice := make([]map[string]interface{}, len(sliceVal))
					for i, item := range sliceVal {
						// 递归处理每个切片元素
						processed, err := preprocessData(item, nestedTypeInfo)
						if err != nil {
							return nil, err
						}
						processedSlice[i] = processed
					}
					result[fieldName] = processedSlice
				}
			} else {
				// 3.2 处理非切片类型的嵌套结构体
				if nestedMap, ok := val.(map[string]interface{}); ok {
					// 递归处理嵌套map
					processed, err := preprocessData(nestedMap, nestedTypeInfo)
					if err != nil {
						return nil, err
					}
					result[fieldName] = processed
				}
			}
		}
	}

	return result, nil
}

// processRawData 处理原始数据，将其存储到目标结构体的指定字段中
func processRawData(data map[string]interface{}, target interface{}, info typeInfo) {
	// 获取目标结构体的反射值
	val := reflect.ValueOf(target).Elem()

	// 1. 处理原始数据字段(rawDataField)
	if info.rawDataField != "" {
		// 获取原始数据字段的反射值
		rawDataField := val.FieldByName(info.rawDataField)

		// 检查字段是否有效、可设置且为map类型
		if rawDataField.IsValid() && rawDataField.CanSet() && rawDataField.Kind() == reflect.Map {
			// 创建新的map存储原始数据
			rawData := make(map[string]interface{})

			// 遍历原始数据，将当前层级中未映射到结构体字段的数据存入rawData
			for k, v := range data {
				// 检查是否为嵌套字段
				if _, isNested := info.nestedFields[k]; !isNested {
					// 检查字段是否在结构体中有映射
					if _, exists := info.allFields[k]; !exists {
						// 如果字段不存在于结构体映射中，将其添加到原始数据中
						rawData[k] = v
					}
				}
			}

			// 将处理后的原始数据设置到目标字段
			rawDataField.Set(reflect.ValueOf(rawData))
		}
	}

	// 2. 处理嵌套结构体的原始数据
	for fieldName, nestedInfo := range info.nestedFields {
		// 检查字段是否存在原始数据中
		if nestedVal, exists := data[fieldName]; exists {
			// 获取嵌套字段的反射值
			nestedField := val.FieldByName(nestedInfo.fieldName)
			if !nestedField.IsValid() {
				continue
			}

			if nestedInfo.isSlice {
				// 2.1 处理切片类型的嵌套结构体
				sliceVal, ok := nestedVal.([]map[string]interface{})
				if !ok {
					continue
				}

				// 确保嵌套字段是切片类型且不为nil
				if nestedField.Kind() != reflect.Slice || nestedField.IsNil() {
					continue
				}

				// 遍历切片中的每个元素
				for i := 0; i < nestedField.Len() && i < len(sliceVal); i++ {
					// 获取当前元素的反射值
					itemVal := nestedField.Index(i)

					// 处理指针类型的元素
					if itemVal.Kind() == reflect.Ptr {
						if itemVal.IsNil() {
							continue
						}
						itemVal = itemVal.Elem()
					}

					// 确保元素是结构体类型
					if itemVal.Kind() != reflect.Struct {
						continue
					}

					// 获取对应的原始数据
					itemMap := sliceVal[i]
					if !ok {
						continue
					}

								// 获取元素类型的元信息并递归处理
					elemTypeInfo := getTypeInfo(itemVal.Type())
					processRawData(itemMap, itemVal.Addr().Interface(), elemTypeInfo)
				}
			} else {
				// 2.2 处理非切片类型的嵌套结构体
				if nestedField.Kind() == reflect.Ptr {
					if nestedField.IsNil() {
						continue
					}
					nestedField = nestedField.Elem()
				}

				if nestedField.Kind() == reflect.Struct {
					if nestedMap, ok := nestedVal.(map[string]interface{}); ok {
						// 递归处理嵌套结构体的原始数据
						nestedTypeInfo := getTypeInfo(nestedField.Type())
						processRawData(nestedMap, nestedField.Addr().Interface(), nestedTypeInfo)
					}
				}
			}
		}
	}
}
